{"configurations": [{"id": "4E97B8E0-7C2B-4CA7-8821-76791B163EA4", "name": "Test Scheme Action", "options": {}}], "defaultOptions": {"targetForVariableExpansion": {"containerPath": "container:MacChessBase.xcodeproj", "identifier": "8E61480D2DEAB78F0032F0FE", "name": "MacChessBase"}}, "testTargets": [{"parallelizable": true, "skippedTests": ["ChessGameViewModelTests", "ChessGameViewModelTests/testFlipBoard()", "ChessGameViewModelTests/testInitialState()", "ChessGameViewModelTests/testLoadGameFromFEN()", "ChessGameViewModelTests/testLoadGameFromPGN()", "ChessGameViewModelTests/testMakeCapture()", "ChessGameViewModelTests/testMakeInvalidMove()", "ChessGameViewModelTests/testMakeValidMove()", "ChessGameViewModelTests/testMoveNavigation()", "ChessGameViewModelTests/testNewGame()", "ChessGameViewModelTests/testSquareSelection()", "ChessGameViewModelTests/testSquareSelectionWithMove()", "ChessKitExtensionsTests", "ChessKitExtensionsTests/testAllSquareTransformations()", "ChessKitExtensionsTests/testBoardEdgeSquares()", "ChessKitExtensionsTests/testBoardSizeCalculations()", "ChessKitExtensionsTests/testBoardSizeCalculationsWithDifferentSizes()", "ChessKitExtensionsTests/testCoordinateRangeValidation()", "ChessKitExtensionsTests/testCoordinateTransformPerformance()", "ChessKitExtensionsTests/testGameExtensions()", "ChessKitExtensionsTests/testMoveExtensions()", "ChessKitExtensionsTests/testNegativeCoordinates()", "ChessKitExtensionsTests/testPieceExtensions()", "ChessKitExtensionsTests/testPointOutsideBoard()", "ChessKitExtensionsTests/testPointToSquarePerformance()", "ChessKitExtensionsTests/testPointToSquareTransform()", "ChessKitExtensionsTests/testPointToSquareTransformFlipped()", "ChessKitExtensionsTests/testPositionExtensions()", "ChessKitExtensionsTests/testSquareExtensions()", "ChessKitExtensionsTests/testSquareToCoordinateTransform()", "ChessKitExtensionsTests/testSquareToCoordinateTransformFlipped()", "ChessKitExtensionsTests/testTransformationConsistency()", "ChessKitExtensionsTests/testVeryLargeCoordinates()", "EngineManagerTests", "EngineManagerTests/testAnalysisPerformance()", "EngineManagerTests/testAnalyzeComplexPosition()", "EngineManagerTests/testAnalyzeCustomPosition()", "EngineManagerTests/testAnalyzeStandardPosition()", "EngineManagerTests/testClearAnalysis()", "EngineManagerTests/testEngineDepthSetting()", "EngineManagerTests/testEngineHashSizeSetting()", "EngineManagerTests/testEngineMemoryUsage()", "EngineManagerTests/testEngineMultiPVSetting()", "EngineManagerTests/testEngineOptions()", "EngineManagerTests/testEngineRestart()", "EngineManagerTests/testEngineShutdown()", "EngineManagerTests/testEngineStartup()", "EngineManagerTests/testEngineStartupFailure()", "EngineManagerTests/testEngineThreadsSetting()", "EngineManagerTests/testEngineVersion()", "EngineManagerTests/testInvalidPositionAnalysis()", "EngineManagerTests/testMultiPVAnalysis()", "EngineManagerTests/testMultiplePositionAnalysisPerformance()", "EngineManagerTests/testPauseAndResumeAnalysis()", "FENValidationTests", "FENValidationTests/testCastlingRightsLogicValidation()", "FENValidationTests/testCheckValidation()", "FENValidationTests/testComplexFENValidationPerformance()", "FENValidationTests/testComplexInvalidPositions()", "FENValidationTests/testComplexValidPositions()", "FENValidationTests/testEnPassantLogicValidation()", "FENValidationTests/testFENValidationPerformance()", "FENValidationTests/testInvalidActiveColor()", "FENValidationTests/testInvalidCastlingRights()", "FENValidationTests/testInvalidEnPassant()", "FENValidationTests/testInvalidFENStructure()", "FENValidationTests/testInvalidMoveCounters()", "FENValidationTests/testInvalidPiecePlacement()", "FENValidationTests/testKingCountValidation()", "FENValidationTests/testPawnPlacementValidation()", "FENValidationTests/testPieceCountValidation()", "FENValidationTests/testValidActiveColor()", "FENValidationTests/testValidCastlingRights()", "FENValidationTests/testValidEnPassant()", "FENValidationTests/testValidMoveCounters()", "FENValidationTests/testValidPiecePlacement()", "FENValidationTests/testValidStandardFEN()", "GameSessionManagerTests", "GameSessionManagerTests/testConcurrentSessionOperations()", "GameSessionManagerTests/testCreateMultipleSessions()", "GameSessionManagerTests/testCreateNewSession()", "GameSessionManagerTests/testInitialState()", "GameSessionManagerTests/testRemoveActiveSession()", "GameSessionManagerTests/testRemoveLastSession()", "GameSessionManagerTests/testRemoveSession()", "GameSessionManagerTests/testRenameSession()", "GameSessionManagerTests/testSelectNonexistentSession()", "GameSessionManagerTests/testSelectSession()", "GameSessionManagerTests/testSessionCreationPerformance()", "GameSessionManagerTests/testSessionGameState()", "GameSessionManagerTests/testSessionIndependence()", "GameSessionManagerTests/testSessionSwitchingPerformance()", "MacChessBaseTests", "MacChessBaseTests/example()", "PerformanceTests", "PerformanceTests/testBoardCoordinateTransformationPerformance()", "PerformanceTests/testComplexGamePerformance()", "PerformanceTests/testFENParsingPerformance()", "PerformanceTests/testFENValidationPerformance()", "PerformanceTests/testGameCreationPerformance()", "PerformanceTests/testLegalMovesGenerationPerformance()", "PerformanceTests/testMemoryUsageWithLargeGames()", "PerformanceTests/testMoveExecutionPerformance()", "PerformanceTests/testPGNGenerationPerformance()", "PerformanceTests/testPGNParsingPerformance()", "PerformanceTests/testPositionEvaluationPerformance()", "PerformanceTests/testRapidGameCreationAndDestruction()"], "target": {"containerPath": "container:MacChessBase.xcodeproj", "identifier": "8E6148202DEAB7930032F0FE", "name": "MacChessBaseTests"}}, {"parallelizable": true, "skippedTests": ["MacChessBaseUITests", "MacChessBaseUITests/testAccessibilityElements()", "MacChessBaseUITests/testBoardRenderingPerformance()", "MacChessBaseUITests/testChessBoardInteraction()", "MacChessBaseUITests/testEditMenu()", "MacChessBaseUITests/testEngineAnalysisView()", "MacChessBaseUITests/testExample()", "MacChessBaseUITests/testFileMenu()", "MacChessBaseUITests/testGameMenu()", "MacChessBaseUITests/testGameSessionSidebar()", "MacChessBaseUITests/testInvalidPGNImport()", "MacChessBaseUITests/testKeyboardShortcuts()", "MacChessBaseUITests/testLaunchPerformance()", "MacChessBaseUITests/testMoveNotationView()", "MacChessBaseUITests/testMultipleWindows()", "MacChessBaseUITests/testNavigationPerformance()", "MacChessBaseUITests/testPositionEditor()", "MacChessBaseUITests/testWindowResize()", "MacChessBaseUITestsLaunchTests", "MacChessBaseUITestsLaunchTests/testLaunch()"], "target": {"containerPath": "container:MacChessBase.xcodeproj", "identifier": "8E61482A2DEAB7930032F0FE", "name": "MacChessBaseUITests"}}], "version": 1}