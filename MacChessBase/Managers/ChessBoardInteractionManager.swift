//
//  ChessBoardInteractionManager.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/8/3.
//

import SwiftUI
import ChessKit
import Foundation

/// Manager responsible for handling all chess board interactions including click-based and drag-based moves
@MainActor
final class ChessBoardInteractionManager: ObservableObject {
    
    // MARK: - Dependencies
    weak var session: GameSession?
    weak var variationManager: VariationManager?
    weak var soundManager: SoundManager?
    
    // MARK: - Published Properties
    @Published var selectedSquare: Square?
    @Published var possibleMoves: [Square] = []
    @Published var lastMove: Move?
    
    // MARK: - Reverse Drag State
    @Published var isReverseDragActive: Bool = false
    @Published var reverseDragTarget: Square?
    @Published var reverseDragValidSources: [Square] = []
    
    // MARK: - Promotion State
    @Published var promotionMove: Move?
    @Published var showPromotionDialog = false
    
    // MARK: - Computed Properties
    private var board: Board {
        session?.board ?? Board()
    }
    
    private var currentMoveIndex: MoveTree.MoveIndex {
        session?.currentMoveIndex ?? MoveTree.minimumIndex
    }
    
    // MARK: - Initialization
    init() {}
    
    // MARK: - Dependency Injection
    func configure(session: GameSession?, variationManager: VariationManager?, soundManager: SoundManager?) {
        self.session = session
        self.variationManager = variationManager
        self.soundManager = soundManager
    }
    
    // MARK: - Click-Based Interaction
    
    /// Handles square selection and piece movement via click
    func handleSquarePress(_ square: Square) {
        if let selectedSquare = selectedSquare {
            // Normal mode with a piece already selected
            attemptMove(from: selectedSquare, to: square)
        } else {
            // No selection, try to select a piece
            selectSquare(square)
        }
    }
    
    /// Selects a square if it contains a piece of the current player
    private func selectSquare(_ square: Square) {
        guard let piece = board.position.piece(at: square),
              piece.color == board.position.sideToMove else {
            clearSelection()
            return
        }
        
        selectedSquare = square
        possibleMoves = board.legalMoves(forPieceAt: square)
    }
    
    /// Attempts to move a piece from one square to another
    func attemptMove(from startSquare: Square, to endSquare: Square) {
        // If clicking the same square, deselect
        if startSquare == endSquare {
            clearAllSelections()
            return
        }
        
        // If clicking another piece of the same color, select it instead
        if let piece = board.position.piece(at: endSquare),
           piece.color == board.position.sideToMove {
            selectSquare(endSquare)
            return
        }
        
        // Check if the move is legal first, but don't execute it yet
        if board.canMove(pieceAt: startSquare, to: endSquare) {
            // Create a temporary move to check for variations
            var tempBoard = board
            if let tempMove = tempBoard.move(pieceAt: startSquare, to: endSquare) {
                // Check if we need to show variation creation dialog before making the actual move
                if variationManager?.shouldShowVariationCreationDialog(for: tempMove, currentMoveIndex: currentMoveIndex) == true {
                    // Store the move and show dialog, but don't execute the move yet
                    let existingNextIndex = getExistingNextMoveIndex(for: tempMove)
                    variationManager?.prepareVariationCreation(move: tempMove, fromIndex: currentMoveIndex, existingNextIndex: existingNextIndex)
                    clearAllSelections()
                    return
                }
                
                // No variation dialog needed, execute the move directly
                var mutableBoard = board
                if let actualMove = mutableBoard.move(pieceAt: startSquare, to: endSquare) {
                    handleMoveResult(actualMove)
                }
            }
        } else {
            // Invalid move, clear all selections
            clearAllSelections()
        }
    }
    
    // MARK: - Drag-Based Interaction
    
    /// Validates if a drag operation can start for the given piece and square
    func validateDragStart(piece: Piece, from square: Square) -> Bool {
        guard piece.color == board.position.sideToMove else { return false }
        return true
    }
    
    /// Checks if a piece at the given square can be moved (has legal moves)
    func canMovePiece(at square: Square) -> Bool {
        guard let piece = board.position.piece(at: square),
              piece.color == board.position.sideToMove else { return false }
        return !board.legalMoves(forPieceAt: square).isEmpty
    }
    
    /// Sets the selected square for move validation
    func setSelectedSquare(_ square: Square) {
        selectedSquare = square
        possibleMoves = board.legalMoves(forPieceAt: square)
    }
    
    // MARK: - Reverse Drag Implementation
    
    /// Starts a reverse drag from a target square
    func startReverseDrag(from targetSquare: Square) -> Bool {
        let piece = board.position.piece(at: targetSquare)
        
        print("startReverseDrag from \(targetSquare), piece: \(piece?.description ?? "empty"), sideToMove: \(board.position.sideToMove)")
        
        // Only allow reverse drag from empty squares or opponent pieces
        if piece == nil || piece?.color != board.position.sideToMove {
            let validSources = findSourceSquares(for: targetSquare)
            
            // Enter reverse drag mode
            isReverseDragActive = true
            reverseDragTarget = targetSquare
            reverseDragValidSources = validSources
            clearSelection() // Clear normal selection
            
            print("startReverseDrag success, found \(validSources.count) source pieces: \(validSources)")
            return true
        }
        print("startReverseDrag failed - trying to drag own piece")
        return false
    }
    
    /// Completes a reverse drag to a source square
    func completeReverseDrag(to sourceSquare: Square) {
        guard isReverseDragActive,
              let targetSquare = reverseDragTarget else {
            cancelReverseDrag()
            return
        }
        
        print("completeReverseDrag from \(sourceSquare) to \(targetSquare)")
        
        // Check if the source square is valid for this reverse drag
        if reverseDragValidSources.contains(sourceSquare) {
            print("Valid reverse drag move, attempting move from \(sourceSquare) to \(targetSquare)")
            attemptMove(from: sourceSquare, to: targetSquare)
        } else {
            print("Invalid reverse drag move - source \(sourceSquare) not in valid sources: \(reverseDragValidSources)")
        }
        
        cancelReverseDrag()
    }
    
    /// Cancels the reverse drag operation
    func cancelReverseDrag() {
        isReverseDragActive = false
        reverseDragTarget = nil
        reverseDragValidSources = []
    }
    
    /// Finds all squares containing pieces that can move to the target square
    private func findSourceSquares(for targetSquare: Square) -> [Square] {
        var sourceSquares: [Square] = []
        
        // Check all squares on the board
        for rank in 1...8 {
            for file in ["a", "b", "c", "d", "e", "f", "g", "h"] {
                let square = Square("\(file)\(rank)")
                let piece = board.position.piece(at: square)
                
                // Only check pieces of the current player
                guard let piece = piece, piece.color == board.position.sideToMove else { continue }
                
                // Check if this piece can move to the target square
                let legalMoves = board.legalMoves(forPieceAt: square)
                if legalMoves.contains(targetSquare) {
                    sourceSquares.append(square)
                }
            }
        }
        
        print("findSourceSquares for \(targetSquare): found \(sourceSquares.count) pieces - \(sourceSquares)")
        return sourceSquares
    }
    
    // MARK: - Move Execution and Result Handling
    
    /// Handles the result of a successful move (move has already been executed on the board)
    private func handleMoveResult(_ move: Move) {
        lastMove = move
        
        guard let metaMove = move.metaMove else {
            return
        }
        
        // Play move sound
        soundManager?.playMoveSound(for: move)
        
        // Check for pawn promotion
        if metaMove.piece.kind == .pawn &&
           (metaMove.end.rank.value == 8 || metaMove.end.rank.value == 1) &&
            metaMove.promotedPiece == nil {
            promotionMove = move
            showPromotionDialog = true
            clearAllSelections()
            return
        }
        
        // Complete the move sequence - the move has already been executed on the board
        completeExecutedMoveSequence(move)
    }
    
    /// Completes a move sequence for a move that has already been executed on the board
    private func completeExecutedMoveSequence(_ move: Move) {
        guard let session = session else { return }
        
        // Check if there's already a next move that would create a variation
        if let existingNextIndex = session.game.moves.hasNextMove(containing: move, for: currentMoveIndex) {
            // Move already exists, just navigate to it
            session.goToMove(at: existingNextIndex)
            clearAllSelections()
            return
        }
        
        // No existing moves, proceed with normal move creation in the game tree
        // (the move has already been executed on the board)
        let newIndex = session.makeMoveInGame(move, from: currentMoveIndex)
        session.goToMove(at: newIndex)
        
        clearAllSelections()
    }
    
    /// Executes a move with the specified variation creation option
    func executeMove(_ move: Move, option: VariationManager.VariationCreationOption) {
        guard let session = session else { return }
        let fromIndex = variationManager?.pendingMoveFromIndex ?? currentMoveIndex
        
        guard let metaMove = move.metaMove else {
            return
        }
        
        // First, execute the move on the board
        var mutableBoard = board
        guard let executedMove = mutableBoard.move(pieceAt: metaMove.start, to: metaMove.end) else {
            print("Failed to execute move on board")
            return
        }
        
        // Update last move and play sound
        lastMove = executedMove
        soundManager?.playMoveSound(for: executedMove)
        
        // Check for pawn promotion
        if executedMove.metaMove!.piece.kind == Piece.Kind.pawn &&
            (executedMove.metaMove!.end.rank.value == 8 || executedMove.metaMove!.end.rank.value == 1) &&
            executedMove.metaMove!.promotedPiece == nil {
            promotionMove = executedMove
            showPromotionDialog = true
            clearAllSelections()
            return
        }
        
        // Add the move to the game tree based on the selected option
        switch option {
        case .newVariation:
            // Default behavior: create new variation
            let newIndex = session.makeMoveInGame(executedMove, from: fromIndex)
            session.goToMove(at: newIndex)
            
        case .newMainLine:
            // Create new variation and promote it
            let newMoveIndex = session.makeMoveInGame(executedMove, from: fromIndex)
            _ = session.promoteVariation(at: newMoveIndex)
            session.goToMove(at: newMoveIndex)
            
        case .overwrite:
            // Delete all moves after the current position (both next and children)
            _ = session.overwriteMove(executedMove, from: fromIndex)
            // goToMove is called within overwriteMove
        }
        
        clearAllSelections()
        
        // Clear pending move data in variation manager
        variationManager?.pendingMove = nil
        variationManager?.pendingMoveFromIndex = nil
        variationManager?.existingNextMoveIndex = nil
    }
    
    /// Completes a pawn promotion
    func completePromotion(to pieceKind: Piece.Kind) {
        guard let promotionMove = promotionMove else { return }
        
        var mutableBoard = board
        let completedMove = mutableBoard.completePromotion(of: promotionMove, to: pieceKind)
        
        // Since promotion moves are always created as part of executeMove,
        // we need to complete the move sequence
        completeExecutedMoveSequence(completedMove)
        
        self.promotionMove = nil
        showPromotionDialog = false
    }
    
    // MARK: - Selection Management
    
    /// Clears the current selection
    func clearSelection() {
        selectedSquare = nil
        possibleMoves = []
    }
    
    /// Clears all selections (normal and reverse drag)
    func clearAllSelections() {
        clearSelection()
        cancelReverseDrag()
    }
    
    /// Called when game state changes to update interaction state
    func onGameStateChanged() {
        guard let session = session else { return }
        
        // Update last move for highlighting
        if currentMoveIndex != session.game.startingIndex {
            lastMove = session.game.moves.getNodeMove(index: currentMoveIndex)
        } else {
            lastMove = nil
        }
        
        // Clear selections
        clearAllSelections()
    }
    
    // MARK: - Helper Methods
    
    /// Gets the existing next move index for the current position
    private func getExistingNextMoveIndex(for move: Move) -> MoveTree.MoveIndex? {
        guard let session = session else { return nil }
        
        if currentMoveIndex == session.game.startingIndex {
            return session.game.moves.nextIndex(currentIndex: currentMoveIndex)
        }
        return session.game.moves.nextIndex(currentIndex: currentMoveIndex)
    }
}